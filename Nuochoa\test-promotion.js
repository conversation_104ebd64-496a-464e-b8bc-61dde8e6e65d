// Script để test khu<PERSON>ến mãi
import axios from 'axios';

async function testPromotions() {
  try {
    console.log('🔍 Testing promotions...');
    
    // 1. L<PERSON>y danh sách khuyến mãi
    const promotionsResponse = await axios.get('http://localhost:4000/api/promotions/admin/all');
    const promotions = promotionsResponse.data?.data || [];
    
    console.log('📋 Total promotions:', promotions.length);
    
    promotions.forEach((promo, index) => {
      console.log(`\n${index + 1}. ${promo.title}`);
      console.log(`   Status: ${promo.status}`);
      console.log(`   Type: ${promo.type}, Value: ${promo.value}`);
      console.log(`   Start: ${new Date(promo.startDate).toLocaleString()}`);
      console.log(`   End: ${new Date(promo.endDate).toLocaleString()}`);
      console.log(`   Applicable Products: ${promo.condition?.applicableProductIds?.length || 0}`);
      
      // Kiểm tra promotion có active không
      const now = new Date();
      const isActive = promo.status === 'active' &&
                      new Date(promo.startDate) <= now &&
                      new Date(promo.endDate) >= now;
      console.log(`   Is Active: ${isActive}`);
    });
    
    // 2. Lấy danh sách sản phẩm
    const productsResponse = await axios.get('http://localhost:4000/api/product/list');
    const products = productsResponse.data?.data || [];
    
    console.log(`\n📦 Total products: ${products.length}`);
    
    // Kiểm tra 3 sản phẩm đầu tiên
    products.slice(0, 3).forEach((product, index) => {
      console.log(`\n${index + 1}. ${product.name}`);
      console.log(`   Price: ${product.price}`);
      console.log(`   Has Promotion: ${product.hasPromotion}`);
      console.log(`   Discounted Price: ${product.discountedPrice}`);
      console.log(`   Promotion:`, product.promotion);
    });
    
  } catch (error) {
    console.error('❌ Error testing promotions:', error.message);
  }
}

testPromotions();
