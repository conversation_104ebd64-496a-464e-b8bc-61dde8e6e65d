import React from 'react';
import './PriceDisplay.css';

const PriceDisplay = ({
  originalPrice,
  discountedPrice,
  hasPromotion,
  promotion,
  size = 'medium',
  showSavings = true,
  className = '',
  // Thêm prop để hiển thị giá gốc khi không có khuyến mãi
  showOriginalPrice = true
}) => {
  // Debug log
  console.log('💰 PriceDisplay props:', {
    originalPrice,
    discountedPrice,
    hasPromotion,
    promotion,
    className
  });

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  // Kiểm tra xem có nên hiển thị giá gạch ngang không
  const shouldShowStrikethrough = () => {
    if (hasPromotion) return true;
    // Hiển thị giá gạch ngang nếu có originalPrice và nó khác với discountedPrice
    return showOriginalPrice && originalPrice && discountedPrice && originalPrice > discountedPrice;
  };

  // Kiểm tra xem khuyến mãi có điều kiện không
  const hasPromotionCondition = () => {
    return hasPromotion && promotion && promotion.condition && promotion.condition.minOrderAmount > 0;
  };

  const getSavingsAmount = () => {
    if (hasPromotion) {
      return originalPrice - discountedPrice;
    }
    // Tính tiết kiệm từ originalPrice nếu không có khuyến mãi
    if (originalPrice && discountedPrice && originalPrice > discountedPrice) {
      return originalPrice - discountedPrice;
    }
    return 0;
  };

  const getSavingsPercentage = () => {
    if (hasPromotion) {
      return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
    }
    // Tính phần trăm tiết kiệm từ originalPrice nếu không có khuyến mãi
    if (originalPrice && discountedPrice && originalPrice > discountedPrice) {
      return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
    }
    return 0;
  };

  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'price-display-small';
      case 'large':
        return 'price-display-large';
      case 'extra-large':
        return 'price-display-extra-large';
      default:
        return 'price-display-medium';
    }
  };

  return (
    <div className={`price-display ${getSizeClass()} ${className}`}>
      <div className="price-container">
        {hasPromotion && discountedPrice && discountedPrice < originalPrice ? (
          <>
            {/* Giá sau khuyến mãi */}
            <span className="discounted-price">
              {formatPrice(discountedPrice)}
            </span>

            {/* Giá gốc bị gạch */}
            <span className="original-price">
              {formatPrice(originalPrice)}
            </span>

            {/* Hiển thị số tiền tiết kiệm */}
            {showSavings && getSavingsAmount() > 0 && (
              <div className="savings-info">
                <span className="savings-amount">
                  Tiết kiệm: {formatPrice(getSavingsAmount())}
                </span>
                <span className="savings-percentage">
                  ({getSavingsPercentage()}%)
                </span>
              </div>
            )}

            {/* Thông tin khuyến mãi */}
            {promotion && (
              <div className="promotion-info">
                <span className="promotion-label">
                  🎉 {promotion.title}
                </span>
              </div>
            )}
          </>
        ) : (
          /* Giá bình thường */
          <span className="normal-price">
            {formatPrice(originalPrice || discountedPrice)}
          </span>
        )}
      </div>
    </div>
  );
};

export default PriceDisplay;
